<!DOCTYPE html>
<html lang="th">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ScaryScore - RSU Dent</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- PapaParse Library for CSV parsing -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.2/papaparse.min.js"></script>
  <style type="text/tailwindcss">
    /* ลบ .card และ .container-main ออก เพราะใช้ container/mx-auto/w-full/lg:max-w-[75%] ตามตัวอย่าง */
    .spinner {
      @apply inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em];
    }
    .spinner-lg {
      @apply h-8 w-8;
    }
    .badge {
      @apply inline-flex items-center rounded-lg px-2.5 py-1 text-xs font-medium ring-1 ring-inset;
    }
    /* Stat-specific badges: make them smaller */
    .badge.stat-badge {
      @apply px-1 py-0.5 text-[10px];
    }
    .badge-morning {
      @apply bg-blue-50 text-blue-700 ring-blue-600/20;
    }
    .badge-afternoon {
      @apply bg-amber-50 text-amber-700 ring-amber-600/20;
    }
    .badge-evening {
      @apply bg-purple-50 text-purple-700 ring-purple-600/20;
    }
    .badge-work {
      @apply bg-gray-100 text-gray-700 ring-gray-600/20;
    }
    .badge-blue {
      @apply bg-blue-50 text-blue-700 ring-blue-600/20;
    }
    .badge-green {
      @apply bg-green-50 text-green-700 ring-green-600/20;
    }
    .badge-yellow {
      @apply bg-yellow-50 text-yellow-700 ring-yellow-600/20;
    }
    .badge-red {
      @apply bg-red-50 text-red-700 ring-red-600/20;
    }
    /* Tab styles */
    .tab-button {
      @apply transition-colors duration-200;
    }
    .tab-content {
      @apply transition-all duration-200;
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
  <header class="border-b bg-white">
    <div class="container mx-auto px-4 py-4">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
        <div class="flex justify-between items-center md:flex-1">
          <div class="flex flex-col">
            <h1 class="text-2xl font-bold text-gray-900">
              <i class="fa-solid fa-tooth mr-2"></i>Scary<span class="font-light">Score</span>
            </h1>
            <p class="text-xs md:text-sm text-gray-500 mt-1">ระบบประกาศคะแนนสอบ Dent RSU</p>
          </div>
          <div id="user-info" class="text-sm text-gray-700 text-right flex items-center justify-end ml-4 md:hidden"></div>
        </div>
        <div id="user-info-desktop" class="text-sm text-gray-700 text-right flex items-center justify-end hidden md:flex"></div>
      </div>
    </div>
  </header>
  <main class="px-4 py-8">
    <div class="container mx-auto w-full lg:max-w-[75%] space-y-4">
      <div id="main-card" class="bg-white rounded-lg border shadow-sm p-4 mb-4">
        <div id="status" class="mb-4 text-center"></div>
        <div id="content"></div>
        <div id="student-courses"></div>
        <!-- คะแนนแต่ละวิชาจะแสดง inline ใต้การ์ด -->
      </div>
    </div>
  </main>
  <footer class="text-center text-xs text-gray-400 py-2 border-t">&copy; 2025 <EMAIL></footer>
  <script>
    function renderStudentCourses() {
      document.getElementById('student-courses').innerHTML = `
        <div class="flex flex-col items-center justify-center py-8">
          <div class="spinner spinner-lg text-blue-500 mb-2"></div>
          <div class="text-gray-500">กำลังโหลดรายวิชา...</div>
        </div>
      `;
      google.script.run.withSuccessHandler(async function(courses) {
        console.log('courses:', courses);
        if (!courses) {
          document.getElementById('student-courses').innerHTML = '<div class="text-red-500">เกิดข้อผิดพลาดในการโหลดข้อมูลรายวิชา (null)</div>';
          return;
        }
        if (!Array.isArray(courses)) {
          document.getElementById('student-courses').innerHTML = '<div class="text-red-500">ข้อมูลรายวิชาไม่ถูกต้อง: ' + JSON.stringify(courses) + '</div>';
          return;
        }
        if (courses.length === 0) {
          document.getElementById('student-courses').innerHTML = '<div class="text-gray-500 text-center py-8">ไม่พบรายวิชาที่มีคะแนน</div>';
          return;
        }
        // เรียงลำดับ courses ตามวันที่ประกาศล่าสุดไว้ข้างบน
        courses.sort((a, b) => new Date(b.announceDate) - new Date(a.announceDate));
        let html = '<div class="grid gap-4 mt-4">';
        window.selectedCourseIndexes = Array.isArray(window.selectedCourseIndexes) ? window.selectedCourseIndexes : [];
        // preload scores for all courses
        window.courseScoresCache = window.courseScoresCache || {};
        const preloadPromises = courses.map((c, i) => {
          if (window.courseScoresCache[c.scoreSheetName]) return Promise.resolve();
          return new Promise((resolve) => {
            google.script.run.withSuccessHandler(function(result) {
              window.courseScoresCache[c.scoreSheetName] = result;
              resolve();
            }).doGetStudentCourseScores(c.scoreSheetName);
          });
        });
        // preload all
        await Promise.all(preloadPromises);
        for (let i = 0; i < courses.length; i++) {
          const c = courses[i];
          // กำหนด icon และ badge class สำหรับประเภทวิชา (examType)
          let examTypeIcon = 'fa-book';
          let badgeClass = 'badge-work';
          if (c.examType?.toLowerCase().includes('mid')) {
            examTypeIcon = 'fa-pen-to-square'; badgeClass = 'badge-blue';
          } else if (c.examType?.toLowerCase().includes('final')) {
            examTypeIcon = 'fa-graduation-cap'; badgeClass = 'badge-green';
          } else if (c.examType?.toLowerCase().includes('quiz')) {
            examTypeIcon = 'fa-question'; badgeClass = 'badge-yellow';
          } else if (c.examType?.toLowerCase().includes('practical')) {
            examTypeIcon = 'fa-flask'; badgeClass = 'badge-red';
          }
          const isOpen = window.selectedCourseIndexes && window.selectedCourseIndexes.includes(i);
          html += `
            <div class="rounded-xl shadow bg-white mb-3 border border-gray-100">
              <button class="w-full flex flex-row justify-between items-center px-4 py-1.5 focus:outline-none" onclick="toggleCourseScores(${i}, '${c.scoreSheetName}','${c.code}','${c.name}','${c.announceDate}')">
                <div class="flex flex-col justify-center text-left h-full gap-0.5">
                  <div class="font-bold text-blue-700 flex items-center gap-2">
                    <span>${c.code}</span>
                  </div>
                  <div class="text-gray-700 text-base">${c.name}</div>
                </div>
                <div class="flex-1 flex flex-col justify-center items-end text-right gap-0.5 text-xs text-gray-500">
                  <div class="flex flex-row gap-2 mb-0.5">
                    <span class="badge badge-morning"><i class="fa-solid fa-calendar-days mr-1"></i>${c.year}/${c.term}</span>
                    <span class="badge ${badgeClass}">${c.examType}</span>
                  </div>
                  <span>ประกาศ ${new Date(c.announceDate).toLocaleDateString()}</span>
                </div>
                <svg class="w-5 h-5 ml-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}" id="chevron-${i}" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7"/></svg>
              </button>
              
              <div id="course-scores-${i}" class="px-4 pb-1.5 pt-0 text-gray-700 transition-all duration-300 overflow-hidden" style="max-height: ${isOpen ? '1000px' : '0px'};"></div>
            </div>
          `;
        }
        html += '</div>';
        document.getElementById('student-courses').innerHTML = html;
        // แสดงคะแนนถ้ามีการเลือก
        if (Array.isArray(window.selectedCourseIndexes)) {
          for (const idx of window.selectedCourseIndexes) {
            showCourseScoresInline(idx, courses[idx]);
          }
        }
      })
      .withFailureHandler(function(error) {
        document.getElementById('student-courses').innerHTML = '<div class="text-red-500">เกิดข้อผิดพลาด: ' + error.message + '</div>';
        console.error('GAS error:', error);
      })
      .doGetStudentCourses();
    }

    // ฟังก์ชัน toggle แสดง/ซ่อนคะแนนใต้การ์ด
    function toggleCourseScores(idx, sheetName, code, name, announceDate) {
      window.selectedCourseIndexes = Array.isArray(window.selectedCourseIndexes) ? window.selectedCourseIndexes : [];
      const i = window.selectedCourseIndexes.indexOf(idx);
      const chevron = document.getElementById('chevron-' + idx);
      const box = document.getElementById('course-scores-' + idx);
      if (i !== -1) {
        // คลิกซ้ำ = ซ่อน
        window.selectedCourseIndexes.splice(i, 1);
        if (box) {
          box.style.maxHeight = '0px';
          setTimeout(() => { box.innerHTML = ''; }, 300);
        }
        if (chevron) chevron.classList.remove('rotate-180');
        return;
      }
      // เพิ่ม index นี้เข้าไป (multi-expand)
      window.selectedCourseIndexes.push(idx);
      if (chevron) chevron.classList.add('rotate-180');
      showCourseScoresInline(idx, {scoreSheetName: sheetName, code, name, announceDate});
    }

    // แสดงคะแนนใต้การ์ดที่เลือก
    function showCourseScoresInline(idx, course) {
      const el = document.getElementById('course-scores-' + idx);
      if (!el) return;
      // preload: ใช้ cache
      const result = window.courseScoresCache[course.scoreSheetName];
      if (!result || !result.scores || result.scores.length === 0) {
        el.innerHTML = '<div class="text-gray-500">ไม่พบคะแนนในรายวิชานี้</div>';
        el.style.maxHeight = '200px';
        return;
      }
      let html = `<div class="p-2">`;
      html += `<div class="border-t border-gray-200 pt-2 mb-2">`;
      html += `<div class="divide-y">`;
      for (const s of result.scores) {
        const sections = s.sections || [];
        let htmlRight = '';
        if (sections.length === 1) {
          htmlRight = `<div class="font-bold text-blue-700 text-right">${sections[0].score}/${sections[0].fullScore}</div>`;
        } else if (sections.length > 1) {
          let totalScore = 0, totalFull = 0, sectionHtml = '';
          for (const sec of sections) {
            if (typeof sec.score === 'number' && typeof sec.fullScore === 'number') {
              totalScore += sec.score;
              totalFull += sec.fullScore;
            } else if (!isNaN(parseFloat(sec.score)) && !isNaN(parseFloat(sec.fullScore))) {
              totalScore += Number(sec.score);
              totalFull += Number(sec.fullScore);
            }
            sectionHtml += `<div class="text-right text-sm"><span class="text-gray-500">Score${sec.section ? ' ' + sec.section : ''}:</span> <span class="font-bold text-blue-700">${sec.score}/${sec.fullScore}</span></div>`;
          }
          htmlRight = `<div class="flex flex-col items-end gap-1">` +
            sectionHtml +
            `<div class="text-right text-sm"><span class="text-gray-500">Total Score:</span> <span class="font-bold text-blue-700">${totalScore}/${totalFull}</span></div>` +
          `</div>`;
        }
        
        // กำหนด layout ตามจำนวน sections
        const isMultiSection = sections.length > 1;
        const leftSideHtml = isMultiSection 
          ? `<div class="flex flex-col gap-1">` +
            `<span class="font-medium text-gray-700">${s.studentId}</span>` +
            `<span class="text-gray-600">${s.studentName}</span>` +
            `</div>`
          : `<div class="flex flex-row items-center gap-2">` +
            `<span class="font-medium text-gray-700">${s.studentId}</span>` +
            `<span class="text-gray-600">${s.studentName}</span>` +
            `</div>`;
            
        html += `<div class="flex flex-row items-start justify-between py-2 text-sm">` +
          leftSideHtml +
          htmlRight +
        `</div>`;
      }
      html += '</div>';
      html += '</div>';
      // Stat
      const st = result.stats;
      function formatStat(val) {
        if (val === null || val === undefined || isNaN(val)) return '-';
        return Number.isInteger(val) ? val : val.toFixed(2);
      }
              html += `<div class="border-t border-gray-200 pt-2">`;
        html += `<div class="flex flex-wrap gap-1 text-[10px]">` +
          `<span class="badge badge-blue stat-badge">Mean ${formatStat(st.mean)}</span>` +
          `<span class="badge badge-green stat-badge">SD ${formatStat(st.sd)}</span>` +
          `<span class="badge badge-yellow stat-badge">Min ${formatStat(st.min)}</span>` +
          `<span class="badge badge-red stat-badge">Max ${formatStat(st.max)}</span>` +
          `<span class="badge badge-purple stat-badge">Rank ${st.rank ?? '-'}${st.total ? '/' + st.total : ''}</span>` +
          `</div></div>`;
      el.innerHTML = html;
      el.style.maxHeight = '1000px';
    }

    // เรียกหลัง auth success
    function afterLogin() {
      renderStudentCourses();
    }

    document.addEventListener('DOMContentLoaded', function() {
      google.script.run.withSuccessHandler(function(email) {
        if (!email) {
          document.getElementById('status').innerHTML = '<span class="text-red-500">กรุณาเข้าสู่ระบบด้วยบัญชี @rsu.ac.th</span>';
          document.getElementById('content').innerHTML = '';
          document.getElementById('user-info').innerHTML = '';
        } else {
          document.getElementById('status').innerHTML = '';
          // แสดง email โดยไม่แสดง @rsu.ac.th
          const displayEmail = email.replace('@rsu.ac.th', '');
          
          // สร้างอักษรย่อจาก email
          const initials = displayEmail.split('.').map(part => part.charAt(0).toUpperCase()).join('');
          
          const userInfoHtml = `
            <div class="flex flex-col gap-1">
              <div class="flex items-center gap-2">
                <div class="w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center text-xs font-light text-white">
                  <span>${initials}</span>
                </div>
                <span>${displayEmail}</span>
              </div>
              <button id="instructorModeBtn" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-colors hidden">
                Instructor Mode
              </button>
            </div>
          `;
          
          document.getElementById('user-info').innerHTML = userInfoHtml;
          document.getElementById('user-info-desktop').innerHTML = userInfoHtml;
          document.getElementById('content').innerHTML = '';
          
          // ตรวจสอบสิทธิ์ instructor
          checkInstructorStatus();
          afterLogin();
        }
      }).checkRsuAuth();
    });

    // ตรวจสอบสิทธิ์ instructor
    function checkInstructorStatus() {
      google.script.run.withSuccessHandler(function(result) {
        if (result.isInstructor || result.isAdmin) {
          // แสดงปุ่มในทั้งสองตำแหน่ง (mobile และ desktop)
          const instructorBtns = document.querySelectorAll('#instructorModeBtn');
          instructorBtns.forEach(btn => {
            btn.classList.remove('hidden');
            btn.addEventListener('click', showInstructorDashboard);
          });
          // แสดงหน้าค้นหาแทนหน้าหลักของนักศึกษา
          showInstructorDashboard();
        }
      }).doCheckInstructorStatus();
    }

    // แสดงหน้า Instructor Dashboard
    function showInstructorDashboard() {
      const mainContent = document.getElementById('main-card');
      mainContent.innerHTML = `
        <div class="space-y-6">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-900">Instructor Dashboard</h2>
            <button onclick="showStudentView()" class="text-sm text-gray-500 hover:text-gray-700">
              <i class="fas fa-arrow-left mr-1"></i>กลับไปหน้าหลัก
            </button>
          </div>
          
          <!-- Navigation Tabs -->
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
              <button onclick="showInstructorTab('search')" id="tab-search" class="tab-button border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600">
                <i class="fas fa-search mr-2"></i>ค้นหาคะแนน
              </button>
              <button onclick="showInstructorTab('upload')" id="tab-upload" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                <i class="fas fa-upload mr-2"></i>อัปโหลดคะแนน
              </button>
              <button onclick="showInstructorTab('manage')" id="tab-manage" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                <i class="fas fa-cog mr-2"></i>จัดการประกาศ
              </button>
            </nav>
          </div>
          
          <!-- Search Tab Content -->
          <div id="tab-content-search" class="tab-content">
            <div class="bg-white rounded-lg border p-4">
              <h3 class="text-lg font-medium text-gray-900 mb-4">ค้นหาคะแนนนักศึกษา</h3>
              <div class="flex gap-2">
                <input 
                  type="text" 
                  id="studentSearchInput" 
                  placeholder="ค้นหาด้วย ID, ชื่อ, หรือนามสกุล..." 
                  class="flex-1 rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <button 
                  onclick="searchStudentScores()" 
                  class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors"
                >
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>
            <div id="searchResults" class="space-y-4"></div>
          </div>
          
          <!-- Upload Tab Content -->
          <div id="tab-content-upload" class="tab-content hidden">
            <div class="space-y-6">
              <!-- Import/Export Section -->
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-blue-900 mb-4">
                  <i class="fas fa-file-import mr-2"></i>นำเข้า / ส่งออก ข้อมูลคะแนน
                </h3>
                <p class="text-blue-800 mb-4">คุณสามารถอัปโหลดคะแนนนักศึกษาจำนวนมากได้อย่างรวดเร็วโดยใช้ไฟล์ CSV</p>

                <div class="bg-white rounded-md p-4 mb-4">
                  <h4 class="text-sm font-medium text-gray-800 mb-3">
                    <i class="fas fa-list-ol mr-1"></i>ขั้นตอนการใช้งาน
                  </h4>
                  <ol class="text-sm text-gray-700 space-y-2">
                    <li><strong>1. ดาวน์โหลด Template:</strong> กดปุ่ม "ดาวน์โหลด Template" เพื่อรับไฟล์ CSV ที่มีหัวตารางถูกต้อง</li>
                    <li><strong>2. กรอกข้อมูล:</strong> เปิดไฟล์ด้วยโปรแกรม Excel หรือ Google Sheets และกรอกข้อมูลคะแนน</li>
                    <li><strong>3. บันทึกไฟล์:</strong> บันทึกเป็น CSV (UTF-8) เพื่อรองรับภาษาไทย</li>
                    <li><strong>4. อัปโหลด:</strong> เลือกไฟล์ที่กรอกข้อมูลแล้วและกดปุ่ม "อัปโหลดคะแนน"</li>
                  </ol>
                </div>

                <div class="flex flex-wrap gap-3">
                  <button
                    onclick="downloadTemplate()"
                    class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 transition-colors flex items-center"
                  >
                    <i class="fas fa-download mr-2"></i>ดาวน์โหลด Template
                  </button>
                  <div id="templateResult" class="flex-1"></div>
                </div>
              </div>

              <!-- Score Upload Form -->
              <div class="bg-white rounded-lg border p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">
                  <i class="fas fa-upload mr-2 text-blue-500"></i>อัปโหลดคะแนน
                </h3>

                <form id="uploadForm" class="space-y-4">
                  <!-- Course Information -->
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">รหัสวิชา *</label>
                      <input type="text" id="courseCode" required
                             placeholder="เช่น DENT101"
                             class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">ชื่อวิชา *</label>
                      <input type="text" id="courseName" required
                             placeholder="เช่น ทันตกรรมพื้นฐาน"
                             class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">ประเภทสอบ *</label>
                      <select id="examType" required class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">เลือกประเภทสอบ</option>
                        <option value="midterm">สอบกลางภาค</option>
                        <option value="final">สอบปลายภาค</option>
                        <option value="quiz">Quiz</option>
                        <option value="practical">Practical</option>
                        <option value="assignment">Assignment</option>
                      </select>
                    </div>
                  </div>

                  <!-- Academic Year and Term -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">ปีการศึกษา *</label>
                      <input type="text" id="year" required value="2567"
                             class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">ภาคการศึกษา *</label>
                      <select id="term" required class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">เลือกภาคการศึกษา</option>
                        <option value="S">S (มิถุนายน - กรกฎาคม)</option>
                        <option value="1">1 (สิงหาคม - ธันวาคม)</option>
                        <option value="2">2 (มกราคม - พฤษภาคม)</option>
                      </select>
                    </div>
                  </div>

                  <!-- File Upload -->
                  <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                    <div class="space-y-2">
                      <i class="fas fa-cloud-upload-alt text-3xl text-gray-400"></i>
                      <div>
                        <label for="scoreFile" class="cursor-pointer">
                          <span class="text-sm font-medium text-blue-600 hover:text-blue-500">เลือกไฟล์คะแนน</span>
                          <input type="file" id="scoreFile" accept=".csv" required class="hidden">
                        </label>
                        <p class="text-xs text-gray-500 mt-1">รองรับไฟล์ CSV เท่านั้น (ขนาดไม่เกิน 5MB)</p>
                      </div>
                      <div id="fileInfo" class="text-sm text-gray-600 hidden"></div>
                    </div>
                  </div>

                  <!-- Action Buttons -->
                  <div class="flex gap-3 pt-4">
                    <button
                      type="submit"
                      id="uploadBtn"
                      class="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <i class="fas fa-upload mr-2"></i>อัปโหลดคะแนน
                    </button>
                    <button
                      type="button"
                      onclick="resetUploadForm()"
                      class="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 transition-colors flex items-center"
                    >
                      <i class="fas fa-redo mr-2"></i>รีเซ็ต
                    </button>
                  </div>
                </form>
              </div>
              
              <div id="uploadResult" class="space-y-4"></div>
            </div>
          </div>
          
          <!-- Manage Tab Content -->
          <div id="tab-content-manage" class="tab-content hidden">
            <div class="bg-white rounded-lg border p-4">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                <i class="fas fa-cog mr-2 text-blue-500"></i>จัดการประกาศคะแนน
              </h3>
              <div id="manageCoursesList" class="space-y-4">
                <div class="text-center py-8">
                  <div class="spinner spinner-lg text-blue-500 mb-2"></div>
                  <div class="text-gray-500">กำลังโหลดรายวิชา...</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      `;
      
      // เพิ่ม event listener สำหรับ Enter key
      document.getElementById('studentSearchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          searchStudentScores();
        }
      });
      
      // เพิ่ม event listener สำหรับ upload form
      document.getElementById('uploadForm').addEventListener('submit', function(e) {
        e.preventDefault();
        uploadScores();
      });

      // เพิ่ม event listener สำหรับ file input
      document.getElementById('scoreFile').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const fileInfo = document.getElementById('fileInfo');

        if (file) {
          const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB
          fileInfo.innerHTML = `
            <div class="flex items-center justify-center space-x-2">
              <i class="fas fa-file-csv text-green-500"></i>
              <span class="font-medium">${file.name}</span>
              <span class="text-gray-500">(${fileSize} MB)</span>
            </div>
          `;
          fileInfo.classList.remove('hidden');

          // ตรวจสอบขนาดไฟล์
          if (file.size > 5 * 1024 * 1024) { // 5MB
            fileInfo.innerHTML = `
              <div class="flex items-center justify-center space-x-2 text-red-600">
                <i class="fas fa-exclamation-triangle"></i>
                <span>ไฟล์มีขนาดใหญ่เกินไป (${fileSize} MB) กรุณาเลือกไฟล์ที่มีขนาดไม่เกิน 5MB</span>
              </div>
            `;
            e.target.value = ''; // Clear the file input
          }
        } else {
          fileInfo.classList.add('hidden');
        }
      });
      
      // แสดง tab แรก
      showInstructorTab('search');
      
      // โหลดรายวิชาสำหรับจัดการ
      loadInstructorCourses();
    }

    // ค้นหาคะแนนนักศึกษา
    function searchStudentScores() {
      const searchTerm = document.getElementById('studentSearchInput').value.trim();
      if (!searchTerm) {
        alert('กรุณากรอกคำค้นหา');
        return;
      }
      
      const resultsDiv = document.getElementById('searchResults');
      resultsDiv.innerHTML = `
        <div class="text-center p-4">
          <div class="spinner spinner-lg text-blue-500 mb-2"></div>
          <div class="text-gray-500">กำลังค้นหา...</div>
        </div>
      `;
      
      google.script.run
        .withSuccessHandler(function(results) {
          if (results.error) {
            resultsDiv.innerHTML = `<div class="text-red-500 text-center p-4">${results.error}</div>`;
            return;
          }
          
          if (results.length === 0) {
            resultsDiv.innerHTML = `<div class="text-gray-500 text-center p-4">ไม่พบข้อมูลนักศึกษา</div>`;
            return;
          }
          
          // ใช้รูปแบบการ์ดเดียวกับที่นักศึกษาดูคะแนน
          let html = '<div class="grid gap-4 mt-4">';
          
          // จัดกลุ่มผลลัพธ์ตามรายวิชา
          const courseGroups = {};
          results.forEach(result => {
            const courseKey = `${result.courseCode}_${result.examType}_${result.year}_${result.term}`;
            if (!courseGroups[courseKey]) {
              courseGroups[courseKey] = {
                courseInfo: {
                  code: result.courseCode,
                  name: result.courseName,
                  examType: result.examType,
                  year: result.year,
                  term: result.term,
                  announceDate: result.announceDate,
                  scoreSheetName: result.scoreSheetName
                },
                scores: []
              };
            }
            courseGroups[courseKey].scores.push({
              studentId: result.studentId,
              studentName: result.studentName,
              sections: result.sections
            });
          });
          
          // แสดงผลการ์ดแต่ละรายวิชา
          let courseCount = 0;
          Object.values(courseGroups).forEach((courseGroup, courseIndex) => {
            const c = courseGroup.courseInfo;
            const scores = courseGroup.scores;
            
            // กำหนด icon และ badge class สำหรับประเภทวิชา (examType)
            let examTypeIcon = 'fa-book';
            let badgeClass = 'badge-work';
            if (c.examType?.toLowerCase().includes('mid')) {
              examTypeIcon = 'fa-pen-to-square'; badgeClass = 'badge-blue';
            } else if (c.examType?.toLowerCase().includes('final')) {
              examTypeIcon = 'fa-graduation-cap'; badgeClass = 'badge-green';
            } else if (c.examType?.toLowerCase().includes('quiz')) {
              examTypeIcon = 'fa-question'; badgeClass = 'badge-yellow';
            } else if (c.examType?.toLowerCase().includes('practical')) {
              examTypeIcon = 'fa-flask'; badgeClass = 'badge-red';
            }
            
            html += `
              <div class="rounded-xl shadow bg-white mb-3 border border-gray-100" data-course-index="${courseIndex}">
                <div class="w-full flex flex-row justify-between items-center px-4 py-1.5">
                  <div class="flex flex-col justify-center text-left h-full gap-0.5">
                    <div class="font-bold text-blue-700 flex items-center gap-2">
                      <span>${c.code}</span>
                    </div>
                    <div class="text-gray-700 text-base">${c.name}</div>
                  </div>
                  <div class="flex-1 flex flex-col justify-center items-end text-right gap-0.5 text-xs text-gray-500">
                    <div class="flex flex-row gap-2 mb-0.5">
                      <span class="badge badge-morning"><i class="fa-solid fa-calendar-days mr-1"></i>${c.year}/${c.term}</span>
                      <span class="badge ${badgeClass}">${c.examType}</span>
                    </div>
                    <span>ประกาศ ${new Date(c.announceDate).toLocaleDateString()}</span>
                  </div>
                </div>
                
                <div class="px-4 pb-1.5 pt-0 text-gray-700">
                  <div class="p-2">
                    <div class="border-t border-gray-200 pt-2 mb-2">
                      <div class="divide-y">`;
            
            // แสดงคะแนนของนักศึกษาทั้งหมดในรายวิชานี้
            scores.forEach(s => {
              const sections = s.sections || [];
              let htmlRight = '';
              if (sections.length === 1) {
                htmlRight = `<div class="font-bold text-blue-700 text-right">${sections[0].score}/${sections[0].fullScore}</div>`;
              } else if (sections.length > 1) {
                let totalScore = 0, totalFull = 0, sectionHtml = '';
                for (const sec of sections) {
                  if (typeof sec.score === 'number' && typeof sec.fullScore === 'number') {
                    totalScore += sec.score;
                    totalFull += sec.fullScore;
                  } else if (!isNaN(parseFloat(sec.score)) && !isNaN(parseFloat(sec.fullScore))) {
                    totalScore += Number(sec.score);
                    totalFull += Number(sec.fullScore);
                  }
                  sectionHtml += `<div class="text-right text-sm"><span class="text-gray-500">Score${sec.section ? ' ' + sec.section : ''}:</span> <span class="font-bold text-blue-700">${sec.score}/${sec.fullScore}</span></div>`;
                }
                htmlRight = `<div class="flex flex-col items-end gap-1">` +
                  sectionHtml +
                  `<div class="text-right text-sm"><span class="text-gray-500">Total Score:</span> <span class="font-bold text-blue-700">${totalScore}/${totalFull}</span></div>` +
                `</div>`;
              }
              
              // กำหนด layout ตามจำนวน sections
              const isMultiSection = sections.length > 1;
              const leftSideHtml = isMultiSection 
                ? `<div class="flex flex-col gap-1">` +
                  `<span class="font-medium text-gray-700">${s.studentId}</span>` +
                  `<span class="text-gray-600">${s.studentName}</span>` +
                  `</div>`
                : `<div class="flex flex-row items-center gap-2">` +
                  `<span class="font-medium text-gray-700">${s.studentId}</span>` +
                  `<span class="text-gray-600">${s.studentName}</span>` +
                  `</div>`;
                  
              html += `<div class="flex flex-row items-start justify-between py-2 text-sm">` +
                leftSideHtml +
                htmlRight +
              `</div>`;
            });
            
            html += `
                      </div>
                    </div>
                    <div class="stat-placeholder-${courseIndex}"></div>
                  </div>
                </div>
              </div>
            `;
            
            courseCount++;
          });
          
          html += '</div>';
          resultsDiv.innerHTML = html;
          
          // ดึง stat ของแต่ละรายวิชาจากนักศึกษาทั้งหมด
          Object.values(courseGroups).forEach((courseGroup, courseIndex) => {
            const c = courseGroup.courseInfo;
            
            google.script.run.withSuccessHandler(function(courseStats) {
              if (courseStats && courseStats.stats) {
                const st = courseStats.stats;
                
                // คำนวณ rank สำหรับคนที่ค้นหา
                const searchResults = courseGroup.scores;
                const allScores = courseStats.allScores || [];
                
                // สร้าง rank mapping สำหรับคนที่ค้นหา
                const rankMapping = {};
                searchResults.forEach(student => {
                  const sections = student.sections || [];
                  let totalScore = 0;
                  
                  if (sections.length === 1) {
                    totalScore = parseFloat(sections[0].score);
                  } else if (sections.length > 1) {
                    for (const sec of sections) {
                      if (typeof sec.score === 'number') {
                        totalScore += sec.score;
                      } else if (!isNaN(parseFloat(sec.score))) {
                        totalScore += Number(sec.score);
                      }
                    }
                  }
                  
                  if (!isNaN(totalScore) && totalScore > 0) {
                    // คำนวณ rank ตามที่กำหนด
                    const rank = calculateRank(totalScore, allScores);
                    rankMapping[student.studentId] = rank;
                  }
                });
                
                // แสดง stat badge
                function formatStat(val) {
                  if (val === null || val === undefined || isNaN(val)) return '-';
                  return Number.isInteger(val) ? val : val.toFixed(2);
                }
                
                // สร้าง rank display string
                const rankDisplay = Object.values(rankMapping).length > 0 
                  ? Object.values(rankMapping).join(', ') + `/${st.total || allScores.length}`
                  : `-/${st.total || allScores.length}`;
                
                const statHtml = `
                  <div class="border-t border-gray-200 pt-2">
                    <div class="flex flex-wrap gap-1 text-[10px]">` +
                      `<span class="badge badge-blue stat-badge">Mean ${formatStat(st.mean)}</span>` +
                      `<span class="badge badge-green stat-badge">SD ${formatStat(st.sd)}</span>` +
                      `<span class="badge badge-yellow stat-badge">Min ${formatStat(st.min)}</span>` +
                      `<span class="badge badge-red stat-badge">Max ${formatStat(st.max)}</span>` +
                      `<span class="badge badge-purple stat-badge">Rank ${rankDisplay}</span>` +
                    `</div>
                  </div>
                `;
                
                const placeholder = document.querySelector(`.stat-placeholder-${courseIndex}`);
                if (placeholder) {
                  placeholder.innerHTML = statHtml;
                }
              }
            }).doGetStudentCourseScores(c.scoreSheetName);
          });
        })
        .withFailureHandler(function(error) {
          resultsDiv.innerHTML = `<div class="text-red-500 text-center p-4">เกิดข้อผิดพลาด: ${error.message}</div>`;
        })
        .doSearchStudentScores(searchTerm);
    }

    // ฟังก์ชันคำนวณสถิติเหมือนกับ db.gs
    function calculateStats(scores) {
      if (!scores || scores.length === 0) return {};
      
      const n = scores.length;
      const mean = scores.reduce((a, b) => a + Number(b), 0) / n;
      const sd = Math.sqrt(scores.map(x => Math.pow(Number(x) - mean, 2)).reduce((a, b) => a + b, 0) / n);
      const min = Math.min.apply(null, scores);
      const max = Math.max.apply(null, scores);
      
      return { 
        mean: mean, 
        sd: sd, 
        min: min, 
        max: max, 
        count: n, 
        total: n 
      };
    }

    // ฟังก์ชันคำนวณ rank ตามที่กำหนด
    function calculateRank(studentScore, allScores) {
      if (!allScores || allScores.length === 0) return null;
      
      // เรียงคะแนนจากมากไปน้อย
      const sortedScores = [...allScores].sort((a, b) => b - a);
      
      // หา rank ตามที่กำหนด (คะแนนเท่ากันให้ rank เดียวกัน)
      let rank = 1;
      let currentRank = 1;
      let prevScore = null;
      
      for (let i = 0; i < sortedScores.length; i++) {
        const score = sortedScores[i];
        
        if (prevScore !== null && score < prevScore) {
          currentRank = i + 1;
        }
        
        if (score === studentScore) {
          rank = currentRank;
          break;
        }
        
        prevScore = score;
      }
      
      return rank;
    }

    // กลับไปหน้าหลัก
    function showStudentView() {
      renderStudentCourses();
    }

    // === Instructor Dashboard Functions ===

    // แสดง tab ต่างๆ ใน instructor dashboard
    function showInstructorTab(tabName) {
      // ซ่อน content ทั้งหมด
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
      });
      
      // ลบ active state จาก tab ทั้งหมด
      document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
      });
      
      // แสดง content ที่เลือก
      document.getElementById('tab-content-' + tabName).classList.remove('hidden');
      
      // ตั้งค่า active state ให้ tab ที่เลือก
      document.getElementById('tab-' + tabName).classList.remove('border-transparent', 'text-gray-500');
      document.getElementById('tab-' + tabName).classList.add('border-blue-500', 'text-blue-600');
    }

    // ดาวน์โหลด template
    function downloadTemplate() {
      const resultDiv = document.getElementById('uploadResult');
      resultDiv.innerHTML = `
        <div class="text-center p-4">
          <div class="spinner spinner-lg text-blue-500 mb-2"></div>
          <div class="text-gray-500">กำลังสร้าง template...</div>
        </div>
      `;
      
      google.script.run
        .withSuccessHandler(function(result) {
          if (result.error) {
            resultDiv.innerHTML = `<div class="text-red-500 text-center p-4">${result.error}</div>`;
            return;
          }
          
          // ใช้ CSV content ที่สร้างจาก server
          const csvContent = result;
          
          // สร้างและดาวน์โหลดไฟล์
          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
          const link = document.createElement('a');
          const url = URL.createObjectURL(blob);
          link.setAttribute('href', url);
          link.setAttribute('download', 'score_template.csv');
          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          resultDiv.innerHTML = `
            <div class="bg-green-50 border border-green-200 rounded-md p-4">
              <div class="flex">
                <i class="fas fa-check-circle text-green-400 mt-1 mr-3"></i>
                <div>
                  <h4 class="text-sm font-medium text-green-800">ดาวน์โหลด Template สำเร็จ</h4>
                  <div class="mt-2 text-sm text-green-700">
                    <p class="font-medium">คำแนะนำ:</p>
                    <ul class="list-disc list-inside mt-1 space-y-1">
                      ${result.instructions.map(instruction => `<li>${instruction}</li>`).join('')}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          `;
        })
        .withFailureHandler(function(error) {
          resultDiv.innerHTML = `<div class="text-red-500 text-center p-4">เกิดข้อผิดพลาด: ${error.message}</div>`;
        })
        .doDownloadCSVTemplate();
    }

    // อัปโหลดคะแนน
    function uploadScores() {
      const form = document.getElementById('uploadForm');
      const fileInput = document.getElementById('scoreFile');
      const resultDiv = document.getElementById('uploadResult');

      // ตรวจสอบไฟล์
      if (!fileInput.files[0]) {
        alert('กรุณาเลือกไฟล์คะแนน');
        return;
      }

      const file = fileInput.files[0];

      // แสดง loading ขณะประมวลผล
      resultDiv.innerHTML = `
        <div class="text-center p-4">
          <div class="spinner spinner-lg text-blue-500 mb-2"></div>
          <div class="text-gray-500">กำลังอ่านไฟล์...</div>
        </div>
      `;

      // ใช้ PapaParse สำหรับประมวลผล CSV
      Papa.parse(file, {
        header: false, // ไม่ใช้ header เพราะเราต้องการ array ของ arrays
        skipEmptyLines: true,
        encoding: "UTF-8",
        complete: function(results) {
          try {
            // ตรวจสอบ parsing errors
            if (results.errors.length > 0) {
              console.error("CSV Parsing Errors:", results.errors);

              // แสดงรายละเอียดข้อผิดพลาดที่เป็นประโยชน์
              const errorMessages = results.errors.map(error => {
                let message = `แถว ${error.row + 1}: `;
                switch(error.type) {
                  case 'Quotes':
                    message += 'มีปัญหาเกี่ยวกับเครื่องหมายคำพูด (") ในข้อมูล';
                    break;
                  case 'Delimiter':
                    message += 'ไม่พบตัวคั่น (comma) ที่ถูกต้อง';
                    break;
                  case 'FieldMismatch':
                    message += 'จำนวนคอลัมน์ไม่ตรงกับแถวอื่น';
                    break;
                  default:
                    message += error.message || 'ข้อผิดพลาดไม่ทราบสาเหตุ';
                }
                return message;
              }).slice(0, 5); // แสดงแค่ 5 ข้อผิดพลาดแรก

              resultDiv.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                  <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                    <div>
                      <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาดในการอ่านไฟล์ CSV</h4>
                      <div class="mt-2 text-sm text-red-700">
                        <p class="mb-2">พบข้อผิดพลาด ${results.errors.length} รายการ:</p>
                        <ul class="list-disc list-inside space-y-1">
                          ${errorMessages.map(msg => `<li>${msg}</li>`).join('')}
                          ${results.errors.length > 5 ? `<li>และอีก ${results.errors.length - 5} รายการ...</li>` : ''}
                        </ul>
                        <p class="mt-3 font-medium">คำแนะนำ:</p>
                        <ul class="list-disc list-inside space-y-1">
                          <li>ตรวจสอบว่าไฟล์เป็น CSV ที่ถูกต้อง</li>
                          <li>ใช้ UTF-8 encoding เมื่อบันทึกไฟล์</li>
                          <li>ตรวจสอบว่าทุกแถวมีจำนวนคอลัมน์เท่ากัน</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              `;
              return;
            }

            const scoreData = results.data;

            // ตรวจสอบข้อมูล
            if (!scoreData || scoreData.length < 2) {
              resultDiv.innerHTML = `
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <div class="flex">
                    <i class="fas fa-exclamation-triangle text-yellow-400 mt-1 mr-3"></i>
                    <div>
                      <h4 class="text-sm font-medium text-yellow-800">ไฟล์ไม่มีข้อมูลคะแนน</h4>
                      <div class="mt-2 text-sm text-yellow-700">
                        <p>ไฟล์ CSV ต้องมีอย่างน้อย 2 แถว (1 แถวสำหรับหัวตาราง และ 1 แถวสำหรับข้อมูล)</p>
                        <p class="mt-2 font-medium">คำแนะนำ:</p>
                        <ul class="list-disc list-inside space-y-1">
                          <li>ตรวจสอบว่าไฟล์มีข้อมูลครบถ้วน</li>
                          <li>ดาวน์โหลด Template และใช้เป็นแนวทาง</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              `;
              return;
            }

            // แสดงข้อมูลตัวอย่างเพื่อตรวจสอบ
            const previewData = scoreData.slice(0, 3); // แสดง 3 แถวแรก
            let previewHtml = '<div class="bg-gray-50 border border-gray-200 rounded-md p-3 mb-4">';
            previewHtml += '<h4 class="text-sm font-medium text-gray-800 mb-2">ตัวอย่างข้อมูลที่อ่านได้:</h4>';
            previewHtml += '<div class="text-xs text-gray-600 font-mono">';
            previewData.forEach((row, index) => {
              previewHtml += `<div>${index + 1}: ${row.join(' | ')}</div>`;
            });
            previewHtml += '</div>';

            // แสดงข้อมูลคะแนนเฉพาะ
            if (previewData.length > 1) {
              previewHtml += '<div class="mt-2 pt-2 border-t border-gray-300">';
              previewHtml += '<h5 class="text-xs font-medium text-gray-700 mb-1">ข้อมูลคะแนน:</h5>';
              for (let i = 1; i < previewData.length; i++) {
                const row = previewData[i];
                const studentName = row[1] || 'Unknown';
                let scoreInfo = '';

                // ตรวจสอบจำนวนคอลัมน์
                if (row.length <= 5) {
                  // กรณีมีแค่คอลัมน์เดียว
                  const score = row[3] || '0';
                  const fullScore = row[4] || '0';
                  scoreInfo = `${score}/${fullScore}`;
                } else {
                  // กรณีมีหลาย section
                  let totalScore = 0;
                  let totalFull = 0;
                  for (let j = 3; j < row.length; j += 2) {
                    const score = parseFloat(row[j]) || 0;
                    const fullScore = parseFloat(row[j + 1]) || 0;
                    totalScore += score;
                    totalFull += fullScore;
                  }
                  scoreInfo = `Total: ${totalScore}/${totalFull}`;
                }

                previewHtml += `<div class="text-xs">${studentName}: ${scoreInfo}</div>`;
              }
              previewHtml += '</div>';
            }

            previewHtml += '</div>';

            // เตรียมข้อมูลสำหรับอัปโหลด
            const uploadData = {
              courseCode: document.getElementById('courseCode').value,
              courseName: document.getElementById('courseName').value,
              examType: document.getElementById('examType').value,
              year: document.getElementById('year').value,
              term: document.getElementById('term').value,
              scoreData: scoreData
            };

            // แสดงข้อมูลตัวอย่างและ loading
            resultDiv.innerHTML = previewHtml + `
              <div class="text-center p-4">
                <div class="spinner spinner-lg text-blue-500 mb-2"></div>
                <div class="text-gray-500">กำลังอัปโหลดคะแนน...</div>
              </div>
            `;

            // ส่งข้อมูลไปยัง server
            google.script.run
              .withSuccessHandler(function(result) {
                if (result.error) {
                  resultDiv.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-md p-4">
                      <div class="flex">
                        <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                        <div>
                          <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาด</h4>
                          <div class="mt-2 text-sm text-red-700">${result.error}</div>
                        </div>
                      </div>
                    </div>
                  `;
                } else {
                  resultDiv.innerHTML = `
                    <div class="bg-green-50 border border-green-200 rounded-md p-4">
                      <div class="flex">
                        <i class="fas fa-check-circle text-green-400 mt-1 mr-3"></i>
                        <div>
                          <h4 class="text-sm font-medium text-green-800">อัปโหลดสำเร็จ</h4>
                          <div class="mt-2 text-sm text-green-700">
                            <p>รายวิชา: ${result.scoreSheetName}</p>
                            <p>${result.message}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  `;

                  // รีเซ็ตฟอร์ม
                  resetUploadForm();
                }
              })
              .withFailureHandler(function(error) {
                resultDiv.innerHTML = `
                  <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                      <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                      <div>
                        <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาด</h4>
                        <div class="mt-2 text-sm text-red-700">${error.message}</div>
                      </div>
                    </div>
                  </div>
                `;
              })
              .doUploadScores(uploadData);

          } catch (error) {
            resultDiv.innerHTML = `
              <div class="bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                  <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                  <div>
                    <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาดในการประมวลผล</h4>
                    <div class="mt-2 text-sm text-red-700">
                      <p>ข้อผิดพลาด: ${error.message}</p>
                      <p class="mt-2 font-medium">กรุณาลองใหม่อีกครั้ง หรือติดต่อผู้ดูแลระบบ</p>
                    </div>
                  </div>
                </div>
              </div>
            `;
          }
        },
        error: function(error) {
          resultDiv.innerHTML = `
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mt-1 mr-3"></i>
                <div>
                  <h4 class="text-sm font-medium text-red-800">เกิดข้อผิดพลาดในการอ่านไฟล์</h4>
                  <div class="mt-2 text-sm text-red-700">
                    <p>ไม่สามารถอ่านไฟล์ CSV ได้: ${error.message || 'ไม่ทราบสาเหตุ'}</p>
                    <p class="mt-3 font-medium">คำแนะนำ:</p>
                    <ul class="list-disc list-inside space-y-1">
                      <li>ตรวจสอบว่าไฟล์เป็น CSV ที่ถูกต้อง</li>
                      <li>ลองเปิดไฟล์ด้วย Text Editor เพื่อดูเนื้อหา</li>
                      <li>ตรวจสอบขนาดไฟล์ (ไม่ควรเกิน 5MB)</li>
                      <li>ลองใช้ไฟล์ CSV อื่นเพื่อทดสอบ</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          `;
        }
      });
    }

    // รีเซ็ตฟอร์มอัปโหลด
    function resetUploadForm() {
      document.getElementById('uploadForm').reset();
      document.getElementById('year').value = '2567';
      document.getElementById('uploadResult').innerHTML = '';
    }

    // โหลดรายวิชาของอาจารย์สำหรับจัดการ
    function loadInstructorCourses() {
      const coursesListDiv = document.getElementById('manageCoursesList');
      
      google.script.run
        .withSuccessHandler(function(courses) {
          if (courses.error) {
            coursesListDiv.innerHTML = `<div class="text-red-500 text-center p-4">${courses.error}</div>`;
            return;
          }
          
          if (courses.length === 0) {
            coursesListDiv.innerHTML = `
              <div class="text-center py-8">
                <i class="fas fa-inbox text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">ยังไม่มีรายวิชาที่ประกาศคะแนน</p>
              </div>
            `;
            return;
          }
          
          let html = '<div class="space-y-4">';
          courses.forEach((course, index) => {
            const announceDate = new Date(course.announceDate).toLocaleDateString();
            
            html += `
              <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900">${course.code} - ${course.name}</h4>
                    <p class="text-sm text-gray-600">ประเภท: ${course.examType} | ปีการศึกษา: ${course.year}/${course.term}</p>
                    <p class="text-sm text-gray-500">ประกาศเมื่อ: ${announceDate}</p>
                  </div>
                  <div class="flex gap-2 ml-4">
                    <button 
                      onclick="toggleAnnouncement('${course.scoreSheetName}')" 
                      class="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600 transition-colors"
                    >
                      <i class="fas fa-toggle-on mr-1"></i>Toggle
                    </button>
                    <button 
                      onclick="deleteAnnouncement('${course.scoreSheetName}')" 
                      class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600 transition-colors"
                    >
                      <i class="fas fa-trash mr-1"></i>ลบ
                    </button>
                  </div>
                </div>
              </div>
            `;
          });
          html += '</div>';
          
          coursesListDiv.innerHTML = html;
        })
        .withFailureHandler(function(error) {
          coursesListDiv.innerHTML = `<div class="text-red-500 text-center p-4">เกิดข้อผิดพลาด: ${error.message}</div>`;
        })
        .doGetInstructorCourses();
    }

    // Toggle การประกาศคะแนน
    function toggleAnnouncement(scoreSheetName) {
      if (!confirm('ต้องการเปลี่ยนสถานะการประกาศคะแนนหรือไม่?')) {
        return;
      }
      
      google.script.run
        .withSuccessHandler(function(result) {
          if (result.error) {
            alert('เกิดข้อผิดพลาด: ' + result.error);
            return;
          }
          
          alert(result.message);
          loadInstructorCourses(); // โหลดรายวิชาใหม่
        })
        .withFailureHandler(function(error) {
          alert('เกิดข้อผิดพลาด: ' + error.message);
        })
        .doManageScoreAnnouncement('toggle', scoreSheetName);
    }

    // ลบการประกาศคะแนน
    function deleteAnnouncement(scoreSheetName) {
      if (!confirm('ต้องการลบการประกาศคะแนนนี้หรือไม่? การดำเนินการนี้ไม่สามารถยกเลิกได้')) {
        return;
      }
      
      google.script.run
        .withSuccessHandler(function(result) {
          if (result.error) {
            alert('เกิดข้อผิดพลาด: ' + result.error);
            return;
          }
          
          alert(result.message);
          loadInstructorCourses(); // โหลดรายวิชาใหม่
        })
        .withFailureHandler(function(error) {
          alert('เกิดข้อผิดพลาด: ' + error.message);
        })
        .doManageScoreAnnouncement('delete', scoreSheetName);
    }
  </script>
</body>
</html>